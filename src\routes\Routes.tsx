import React, { useCallback, useEffect, useState, Suspense } from "react";
import { Routes, Route } from "react-router-dom";
// import { AuthContext } from "@/context/Auth";
// import { GlobalContext } from "@/context/Global";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "@/components/PublicWrapper";
import { NotFoundPage } from "@/pages/404";
import { SnackBar } from "@/components/SnackBar";
import { SessionExpiredModal } from "@/components/SessionExpiredModal";
import { MemberWrapper } from "@/components/MemberWrapper";

// generatePagesRoutes
import { AdminWrapper } from "@/components/AdminWrapper";

import {
  AdminDashboardPage,
  AdminForgotPage,
  AdminLoginPage,
  AdminProfilePage,
  AdminResetPage,
  LandingPage as _LandingPage,
  MemberLandingPage,
  MemberLoginPage,
  MemberSignUpPage,
  MemberVerifyIdentityPage,
  MagicLoginVerifyPage,
  MemberMagicLoginPage,
  ListAdminWireframeTablePage,
  AdminSignUpPage,
  TestComponents,
  MemberDashboardPage,
  MemberMarketplaceListPage,
  MemberMarketplaceDetailPage,
  MemberListingsListPage,
  MemberAddListingPage,
  MemberEditListingPage,
  MemberViewListingPage,
  MemberTransactionsListPage,
  MemberViewTransactionPage,
  MemberRewardsListPage,
  MemberAvailableDeliveriesPage,
  MemberMyDeliveriesPage,
  MemberAccountPage,
  MemberDeliverySettingsPage,
  MemberProfilePage,
  AdminUsersListPage,
  AdminAddUserPage,
  AdminListingsListPage,
  AdminTopUpRequestsListPage,
  AdminTransactionsListPage,
  AdminDisputesAndRefundsListPage,
  AdminDeliveryApplicationsListPage,
  AdminRewardsAndReferralsListPage,
  AdminCategoriesListPage,
  AdminPromotionsAndSponsorshipsListPage,
  AdminPlatformSettingsListPage,
  AdminReportedListingListPage,
  AdminDeliveryAgentComplaintsListPage,
  AdminViewListingPage,
  AdminViewTransactionPage,
  AdminAddCategoryPage,
  AdminEditCategoryPage,
  AdminDeliveryAgentComplaintDetailsPage,
  MemberInboxPage,
  MemberNotificationsListPage,
} from "./LazyLoad";

import EditWireframePage from "@/pages/Admin/Edit/EditWireframePage";
import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { RouteChangeModal } from "@/components/RouteChangeModal";
import { MkdLoader } from "@/components/MkdLoader";

export interface DynamicWrapperProps {
  isAuthenticated?: boolean;
  role?: RoleEnum;
  children: React.ReactNode;
}

export const DynamicWrapper: React.FC<DynamicWrapperProps> = ({
  isAuthenticated,
  role,
  children,
}) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return <AdminWrapper>{children}</AdminWrapper>;
    }
    if (role && [RoleEnum.MEMBER].includes(role)) {
      return <MemberWrapper>{children}</MemberWrapper>;
    }
  }
};

export interface NotFoundProps {
  isAuthenticated?: boolean;
  role?: RoleEnum | null;
}

export const NotFound: React.FC<NotFoundProps> = ({
  isAuthenticated,
  role,
}) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  // Debug: Check authentication state

  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }
    if (role && [RoleEnum.MEMBER].includes(role)) {
      return (
        <MemberWrapper>
          <NotFoundPage />
        </MemberWrapper>
      );
    }
  }
};

export default () => {
  const {
    globalState,
    globalDispatch: dispatch,
    authState: state,
    setGlobalState,
  } = useContexts();

  const isOpen = globalState?.isOpen ?? false;
  const openRouteChangeModal = globalState?.openRouteChangeModal ?? false;

  const [screenSize, setScreenSize] = useState(window.innerWidth);

  // function setDimension(e: Event) {
  //   const target = e.currentTarget as Window;
  //   if (target.innerWidth >= 1024) {
  //     toggleSideBar(true);
  //   } else toggleSideBar(false);
  //   setScreenSize(target.innerWidth);
  // }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const portalChange = useCallback(
    (e: any) => {
      if (
        (e.ctrlKey || e.metaKey) &&
        e.shiftKey &&
        e.altKey &&
        ["r", "R"].includes(e.key)
      ) {
        setGlobalState("openRouteChangeModal", true);
      }
      // console.log("PORTAL CHANGE  >>", openRouteChangeModal, e);
      if (["Escape", "escape", "ESCAPE", "Esc", "esc"].includes(e.key)) {
        setGlobalState("openRouteChangeModal", false);
      }
    },
    [setGlobalState]
  );

  const toggleSideBar = (open: boolean) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener(
      "resize",
      (e) => {
        const target = e.currentTarget as Window;
        if (target.innerWidth >= 1024) {
          toggleSideBar(true);
        } else toggleSideBar(false);
        setScreenSize(target.innerWidth);
      },
      { signal }
    );

    return () => {
      controller.abort();
    };
  }, [screenSize, toggleSideBar]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener("keydown", portalChange, { signal });

    return () => {
      controller.abort();
    };
  }, [portalChange]);

  return (
    <div
      onClick={() => {
        isOpen ? toggleSideBar(false) : null;
      }}
      className={`h-svh grid grid-cols-1 grid-rows-[auto_1fr] min-h-svh max-h-svh overflow-y-hidden overflow-x-hidden bg-background`}
    >
      <Routes>
        <Route
          path="/member/dashboard"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/dashboard"}
              element={<MemberDashboardPage />}
            />
          }
        />

        {/* Member Marketplace Routes */}
        <Route
          path="/member/marketplace"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/marketplace"}
              element={<MemberMarketplaceListPage />}
            />
          }
        />
        <Route
          path="/member/marketplace/:id"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/marketplace/:id"}
              element={<MemberMarketplaceDetailPage />}
            />
          }
        />

        {/* Member Listings Routes */}
        <Route
          path="/member/listings"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/listings"}
              element={<MemberListingsListPage />}
            />
          }
        />
        <Route
          path="/member/listings/add"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/listings/add"}
              element={<MemberAddListingPage />}
            />
          }
        />
        <Route
          path="/member/listings/edit/:id"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/listings/edit/:id"}
              element={<MemberEditListingPage />}
            />
          }
        />
        <Route
          path="/member/listings/view/:id"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/listings/view/:id"}
              element={<MemberViewListingPage />}
            />
          }
        />

        {/* Member Transactions Routes */}
        <Route
          path="/member/transactions"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/transactions"}
              element={<MemberTransactionsListPage />}
            />
          }
        />
        <Route
          path="/member/transactions/view/:id"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/transactions/view/:id"}
              element={<MemberViewTransactionPage />}
            />
          }
        />

        {/* Member Rewards Routes */}
        <Route
          path="/member/rewards"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/rewards"}
              element={<MemberRewardsListPage />}
            />
          }
        />

        {/* Member Delivery Routes */}
        <Route
          path="/member/available-deliveries"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/available-deliveries"}
              element={<MemberAvailableDeliveriesPage />}
            />
          }
        />
        <Route
          path="/member/deliveries"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/deliveries"}
              element={<MemberMyDeliveriesPage />}
            />
          }
        />
        <Route
          path="/member/delivery-settings"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/delivery-settings"}
              element={<MemberDeliverySettingsPage />}
            />
          }
        />

        {/* Member Account Routes */}
        <Route
          path="/member/account"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/account"}
              element={<MemberAccountPage />}
            />
          }
        />
        <Route
          path="/member/profile"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/profile"}
              element={<MemberProfilePage />}
            />
          }
        />
        <Route
          path="/member/inbox"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/inbox"}
              element={<MemberInboxPage />}
            />
          }
        />
        <Route
          path="/member/notifications"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/notifications"}
              element={<MemberNotificationsListPage />}
            />
          }
        />

        <Route
          path="/admin/dashboard"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/dashboard"}
              element={<AdminDashboardPage />}
            />
          }
        />
        <Route
          path="/admin/users"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/users"}
              element={<AdminUsersListPage />}
            />
          }
        />
        <Route
          path="/admin/add-user"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/add-user"}
              element={<AdminAddUserPage />}
            />
          }
        />
        <Route
          path="/admin/listings"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/listings"}
              element={<AdminListingsListPage />}
            />
          }
        />
        <Route
          path="/admin/top-up-requests"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/top-up-requests"}
              element={<AdminTopUpRequestsListPage />}
            />
          }
        />
        <Route
          path="/admin/transactions"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/transactions"}
              element={<AdminTransactionsListPage />}
            />
          }
        />
        <Route
          path="/admin/disputes-refunds"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/disputes-refunds"}
              element={<AdminDisputesAndRefundsListPage />}
            />
          }
        />
        <Route
          path="/admin/delivery-applications"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/delivery-applications"}
              element={<AdminDeliveryApplicationsListPage />}
            />
          }
        />
        <Route
          path="/admin/rewards-referrals"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/rewards-referrals"}
              element={<AdminRewardsAndReferralsListPage />}
            />
          }
        />
        <Route
          path="/admin/categories"
          element={
            <Suspense fallback={<MkdLoader />}>
              <AdminCategoriesListPage />
            </Suspense>
          }
        />
        <Route
          path="/admin/categories/add"
          element={
            <Suspense fallback={<MkdLoader />}>
              <AdminAddCategoryPage />
            </Suspense>
          }
        />
        <Route
          path="/admin/categories/edit/:id"
          element={
            <Suspense fallback={<MkdLoader />}>
              <AdminEditCategoryPage />
            </Suspense>
          }
        />
        <Route
          path="/admin/promotions-sponsorships"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/promotions-sponsorships"}
              element={<AdminPromotionsAndSponsorshipsListPage />}
            />
          }
        />
        <Route
          path="/admin/platform-settings"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/platform-settings"}
              element={<AdminPlatformSettingsListPage />}
            />
          }
        />
        <Route
          path="/admin/reported-listing"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/reported-listing"}
              element={<AdminReportedListingListPage />}
            />
          }
        />
        <Route
          path="/admin/listings/view/:id"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/listings/view/:id"}
              element={<AdminViewListingPage />}
            />
          }
        />
        <Route
          path="/admin/delivery-agent-complaints"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/delivery-agent-complaints"}
              element={
                <Suspense fallback={<MkdLoader />}>
                  <AdminDeliveryAgentComplaintsListPage />
                </Suspense>
              }
            />
          }
        />
        <Route
          path="/admin/delivery-agent-complaints/:id"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/delivery-agent-complaints/:id"}
              element={
                <Suspense fallback={<MkdLoader />}>
                  <AdminDeliveryAgentComplaintDetailsPage />
                </Suspense>
              }
            />
          }
        />
        <Route
          path="/admin/profile"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/profile"}
              element={
                <AdminWrapper>
                  <AdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/view-transaction/:id"
          element={<AdminViewTransactionPage />}
        />

        <Route
          path="/admin/edit-wireframe/:id"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/edit-wireframe/:id"}
              element={
                <AdminWrapper>
                  <EditWireframePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/build"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/build"}
              element={
                <AdminWrapper>
                  <ListAdminWireframeTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/"
          element={<PublicRoute path={"/"} element={<MemberLandingPage />} />}
        />
        <Route
          path="/admin/login"
          element={
            <PublicRoute
              path={"/admin/login"}
              element={
                <PublicWrapper>
                  <AdminLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/member/login"
          element={
            <PublicRoute
              path={"/member/login"}
              element={
                <PublicWrapper>
                  <MemberLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/member/sign-up"
          element={
            <PublicRoute
              path={"/member/sign-up"}
              element={
                <PublicWrapper>
                  <MemberSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/member/verify-identity"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/member/verify-identity"}
              element={<MemberVerifyIdentityPage />}
            />
          }
        />
        <Route
          path="/admin/sign-up"
          element={
            <PublicRoute
              path={"/admin/sign-up"}
              element={
                <PublicWrapper>
                  <AdminSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/forgot"
          element={
            <PublicRoute
              path={"/admin/forgot"}
              element={
                <PublicWrapper>
                  <AdminForgotPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/reset"
          element={
            <PublicRoute
              path={"/admin/reset"}
              element={
                <PublicWrapper>
                  <AdminResetPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login"
          element={
            <PublicRoute
              path={"/magic-login"}
              element={
                <PublicWrapper>
                  <MemberMagicLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login/verify"
          element={
            <PublicRoute
              path={"/magic-login/verify"}
              element={
                <PublicWrapper>
                  <MagicLoginVerifyPage />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Custom Routes */}

        <Route
          path="/test-components"
          element={
            <PublicRoute
              path={"/test-components"}
              element={
                <PublicWrapper>
                  <TestComponents />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path={"*"}
          element={
            <PublicRoute
              path={"*"}
              element={
                <NotFound
                  isAuthenticated={state?.isAuthenticated}
                  role={state?.role as RoleEnum | null}
                />
              }
            />
          }
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />

      <LazyLoad>
        <RouteChangeModal
          isOpen={openRouteChangeModal}
          onClose={() => setGlobalState("openRouteChangeModal", false)}
          options={[
            ...(state?.isAuthenticated
              ? [
                  {
                    name: `${state?.role} Login`,
                    route: `/${state?.role}/login`,
                  },
                  { name: "Test Components", route: "/test-components" },
                ]
              : [
                  { name: "Admin Login", route: "/admin/login" },
                  { name: "Member Login", route: "/member/login" },
                  { name: "Test Components", route: "/test-components" },
                ]),
          ]}
          title="Change Route"
        />
      </LazyLoad>
    </div>
  );
};
