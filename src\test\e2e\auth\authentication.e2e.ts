import { test, expect } from "@playwright/test";
import { TEST_USERS } from "../../utils/fixtures/user.fixture";
import { TEST_CONSTANTS } from "../../utils/setup";

test.describe("Authentication", () => {
  test("should display login form", async ({ page }) => {
    // Navigate to the login page
    await page.goto(TEST_CONSTANTS.ROUTES.LOGIN);

    // Check if the login form is displayed
    await expect(
      page.getByText(/Welcome! Please sign in to your account/i)
    ).toBeVisible();
    await expect(page.getByPlaceholder(/<EMAIL>/i)).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Sign In", exact: true })
    ).toBeVisible();
  });

  test("should show error with invalid credentials", async ({ page }) => {
    // Navigate to the login page
    await page.goto(TEST_CONSTANTS.ROUTES.LOGIN);

    // Fill in the login form with invalid credentials
    await page
      .getByPlaceholder(/<EMAIL>/i)
      .fill(TEST_USERS.INVALID.email);

    // Find password field - it's inside a div with a password label
    const passwordField = page.locator('input[type="password"]');
    await passwordField.fill(TEST_USERS.INVALID.password);

    // Submit the form - be more specific with the selector
    await page.getByRole("button", { name: "Sign In", exact: true }).click();

    // Wait for the form submission to complete and error message to appear
    await page.waitForTimeout(2000);

    // Check if error message is displayed - look for field-specific errors first
    const fieldErrors = page.locator("p.text-field-error");
    const fieldErrorCount = await fieldErrors.count();

    if (fieldErrorCount > 0) {
      // Verify field-specific error message is visible
      await expect(fieldErrors.first()).toBeVisible({
        timeout: TEST_CONSTANTS.TIMEOUT.MEDIUM,
      });

      // Verify the error message contains relevant text
      await expect(fieldErrors.first()).toContainText(
        /invalid|credentials|error/i
      );
    } else {
      // Fallback: Look for any visible error indication (toast, alert, etc.)
      const anyError = page
        .locator(".text-red-500, [class*='error'], .toast, [role='alert']")
        .first();
      await expect(anyError).toBeVisible({
        timeout: TEST_CONSTANTS.TIMEOUT.MEDIUM,
      });

      // Verify the error message contains relevant text
      await expect(anyError).toContainText(/invalid|credentials|error/i);
    }

    // Verify we're still on the login page (login failed)
    expect(page.url()).toContain("/login");
  });

  test.skip("should redirect to dashboard after successful login", async ({
    page,
  }) => {
    // Navigate to the login page
    await page.goto(TEST_CONSTANTS.ROUTES.LOGIN);

    // Fill in the login form with valid credentials
    await page.getByPlaceholder(/<EMAIL>/i).fill(TEST_USERS.ADMIN.email);

    // Find password field - it's inside a div with a password label
    const passwordField = page.locator('input[type="password"]');
    await passwordField.fill(TEST_USERS.ADMIN.password);

    // Submit the form - be more specific with the selector
    await page.getByRole("button", { name: "Sign In", exact: true }).click();

    // Check if redirected to dashboard
    await expect(page).toHaveURL(new RegExp(TEST_CONSTANTS.ROUTES.DASHBOARD), {
      timeout: TEST_CONSTANTS.TIMEOUT.MEDIUM,
    });
  });
});
