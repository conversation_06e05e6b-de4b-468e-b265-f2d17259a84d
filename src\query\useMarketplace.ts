import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface IMarketplaceFilters
  extends Record<string, string | number | boolean | undefined> {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  location?: string;
  type?: string;
  sort?: string;
  minPrice?: number;
  maxPrice?: number;
  sponsoredOnly?: boolean;
  myLocationOnly?: boolean;
  myOffersOnly?: boolean;
  myFavoritesOnly?: boolean;
}

interface ISellerData {
  first_name?: string;
  last_name?: string;
  phone?: string;
  referral_code?: string;
  referral_type?: string;
  terms_accepted?: boolean;
  privacy_accepted?: boolean;
  referrer_name?: string;
}

interface ISeller {
  id: number;
  name: string;
  email: string;
  data: ISellerData | null;
  status: number;
  verify: number;
  rating: number;
  location: string;
  createdAt: string;
}

export interface IMarketplaceListing {
  id: number;
  name: string;
  description: string;
  price: string;
  discountPrice?: string;
  type: string;
  category: string;
  status: string;
  images: string[];
  image: string;
  seller: ISeller;
  rating: number;
  sponsored: boolean;
  location?: string;
  viewCount: number;
  favoriteCount: number;
  isFavorited: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IUserLocation {
  id: number;
  address?: string;
  city: string;
  province: string;
  postalCode?: string;
  country: string;
  latitude?: number;
  longitude?: number;
  isPrimary: boolean;
  locationType: string;
  fullAddress: string;
  createdAt: string;
  updatedAt: string;
}

export interface IAvailableLocations {
  cities: string[];
  provinces: string[];
  grouped: Record<string, string[]>;
  combined: string[];
}

// Marketplace Listings Query
export const useMarketplaceListingsQuery = (
  filters: IMarketplaceFilters = {}
) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["marketplace-listings", filters],
    queryFn: async () => {
      // Filter out undefined values for SDK compatibility
      const cleanFilters = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ) as Record<string, string | number>;

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/marketplace/listings",
        method: "GET",
        params: cleanFilters,
      });
      return response;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes (updated from deprecated cacheTime)
  });
};

// Featured Listings Query
export const useFeaturedListingsQuery = (limit: number = 6) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["featured-listings", limit],
    queryFn: async () => {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/marketplace/featured",
        method: "GET",
        params: { limit },
      });
      return response;
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes (updated from deprecated cacheTime)
  });
};

// User Favorites Query
export const useUserFavoritesQuery = (
  filters: { page?: number; limit?: number } = {}
) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["user-favorites", filters],
    queryFn: async () => {
      console.log("🚀 Fetching user favorites with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/favorites",
          method: "GET",
          params: filters,
        });
        console.log("📡 User Favorites API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ User Favorites API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Add to Favorites Mutation
export const useAddToFavoritesMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (listingId: number) => {
      console.log("🚀 Adding listing to favorites:", listingId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/favorites/${listingId}`,
          method: "POST",
        });
        console.log("📡 Add to Favorites API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Add to Favorites API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully added to favorites:", data);
      success("Added to favorites successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["marketplace-listings"] });
      queryClient.invalidateQueries({ queryKey: ["featured-listings"] });
      queryClient.invalidateQueries({ queryKey: ["user-favorites"] });
    },
    onError: (error: any) => {
      console.error("❌ Add to favorites failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to add to favorites";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Remove from Favorites Mutation
export const useRemoveFromFavoritesMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (listingId: number) => {
      console.log("🚀 Removing listing from favorites:", listingId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/favorites/${listingId}`,
          method: "DELETE",
        });
        console.log("📡 Remove from Favorites API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Remove from Favorites API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully removed from favorites:", data);
      success("Removed from favorites successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["marketplace-listings"] });
      queryClient.invalidateQueries({ queryKey: ["featured-listings"] });
      queryClient.invalidateQueries({ queryKey: ["user-favorites"] });
    },
    onError: (error: any) => {
      console.error("❌ Remove from favorites failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to remove from favorites";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Toggle Favorite Mutation
export const useToggleFavoriteMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (listingId: number) => {
      console.log("🚀 Toggling favorite status for listing:", listingId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/favorites/${listingId}/toggle`,
          method: "POST",
        });
        console.log("📡 Toggle Favorite API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Toggle Favorite API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully toggled favorite:", data);
      const action = data.data?.action;
      const message =
        action === "added" ? "Added to favorites" : "Removed from favorites";
      success(message);

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["marketplace-listings"] });
      queryClient.invalidateQueries({ queryKey: ["featured-listings"] });
      queryClient.invalidateQueries({ queryKey: ["user-favorites"] });
    },
    onError: (error: any) => {
      console.error("❌ Toggle favorite failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to update favorite status";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// User Locations Query
export const useUserLocationsQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["user-locations"],
    queryFn: async () => {
      console.log("🚀 Fetching user locations");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/locations",
          method: "GET",
        });
        console.log("📡 User Locations API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ User Locations API Call Error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Primary Location Query
export const usePrimaryLocationQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["primary-location"],
    queryFn: async () => {
      console.log("🚀 Fetching primary location");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/locations/primary",
          method: "GET",
        });
        console.log("📡 Primary Location API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Primary Location API Call Error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Available Locations Query
export const useAvailableLocationsQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["available-locations"],
    queryFn: async () => {
      console.log("🚀 Fetching available locations");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/locations/available",
          method: "GET",
        });
        console.log("📡 Available Locations API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Available Locations API Call Error:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Create Location Mutation
export const useCreateLocationMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (locationData: {
      address?: string;
      city: string;
      province: string;
      postalCode?: string;
      country?: string;
      latitude?: number;
      longitude?: number;
      isPrimary?: boolean;
      locationType?: string;
    }) => {
      console.log("🚀 Creating new location:", locationData);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/locations",
          method: "POST",
          body: locationData,
        });
        console.log("📡 Create Location API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Create Location API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully created location:", data);
      success("Location created successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["user-locations"] });
      queryClient.invalidateQueries({ queryKey: ["primary-location"] });
      queryClient.invalidateQueries({ queryKey: ["available-locations"] });
    },
    onError: (error: any) => {
      console.error("❌ Create location failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to create location";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Update Location Mutation
export const useUpdateLocationMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({
      locationId,
      locationData,
    }: {
      locationId: number;
      locationData: {
        address?: string;
        city?: string;
        province?: string;
        postalCode?: string;
        country?: string;
        latitude?: number;
        longitude?: number;
        isPrimary?: boolean;
        locationType?: string;
      };
    }) => {
      console.log("🚀 Updating location:", locationId, locationData);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/locations/${locationId}`,
          method: "PUT",
          body: locationData,
        });
        console.log("📡 Update Location API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Location API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully updated location:", data);
      success("Location updated successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["user-locations"] });
      queryClient.invalidateQueries({ queryKey: ["primary-location"] });
    },
    onError: (error: any) => {
      console.error("❌ Update location failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to update location";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Delete Location Mutation
export const useDeleteLocationMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (locationId: number) => {
      console.log("🚀 Deleting location:", locationId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/locations/${locationId}`,
          method: "DELETE",
        });
        console.log("📡 Delete Location API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Delete Location API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully deleted location:", data);
      success("Location deleted successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["user-locations"] });
      queryClient.invalidateQueries({ queryKey: ["primary-location"] });
      queryClient.invalidateQueries({ queryKey: ["available-locations"] });
    },
    onError: (error: any) => {
      console.error("❌ Delete location failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to delete location";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Set Primary Location Mutation
export const useSetPrimaryLocationMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (locationId: number) => {
      console.log("🚀 Setting primary location:", locationId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/locations/${locationId}/set-primary`,
          method: "POST",
        });
        console.log("📡 Set Primary Location API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Set Primary Location API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully set primary location:", data);
      success("Primary location updated successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["user-locations"] });
      queryClient.invalidateQueries({ queryKey: ["primary-location"] });
    },
    onError: (error: any) => {
      console.error("❌ Set primary location failed:", error);
      const errorMessage =
        error?.response?.data?.message || "Failed to set primary location";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};
