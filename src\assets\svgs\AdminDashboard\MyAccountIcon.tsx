import React from "react";

const MyAccountIcon = ({ className }: { className?: string }) => (
  <svg
    className={`h-4 w-4 ${className}`}
    // width="14"
    // height="16"
    viewBox="0 0 14 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16_4892)">
      <g clip-path="url(#clip1_16_4892)">
        <path
          d="M7 8C8.06087 8 9.07828 7.57857 9.82843 6.82843C10.5786 6.07828 11 5.06087 11 4C11 2.93913 10.5786 1.92172 9.82843 1.17157C9.07828 0.421427 8.06087 0 7 0C5.93913 0 4.92172 0.421427 4.17157 1.17157C3.42143 1.92172 3 2.93913 3 4C3 5.06087 3.42143 6.07828 4.17157 6.82843C4.92172 7.57857 5.93913 8 7 8ZM5.57188 9.5C2.49375 9.5 0 11.9937 0 15.0719C0 15.5844 0.415625 16 0.928125 16H13.0719C13.5844 16 14 15.5844 14 15.0719C14 11.9937 11.5063 9.5 8.42813 9.5H5.57188Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_16_4892">
        <rect width="14" height="16" fill="white" />
      </clipPath>
      <clipPath id="clip1_16_4892">
        <path d="M0 0H14V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>

  // <svg
  //   xmlns="http://www.w3.org/2000/svg"
  //   className={`h-6 w-6 ${className}`}
  //   fill="none"
  //   viewBox="0 0 24 24"
  //   stroke="currentColor"
  //   strokeWidth={2}
  // >
  //   <path
  //     strokeLinecap="round"
  //     strokeLinejoin="round"
  //     d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
  //   />
  // </svg>
);

export default MyAccountIcon;
