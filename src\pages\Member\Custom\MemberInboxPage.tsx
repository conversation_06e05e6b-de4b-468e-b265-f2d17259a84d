import React, { useState, useEffect, useRef, useCallback } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";

// Interface definitions
interface Conversation {
  id: number;
  listing: {
    id: number;
    name: string;
    price: string;
    image?: string;
  };
  other_user: {
    id: number;
    name: string;
    photo?: string;
  };
  last_message: {
    text: string;
    type: string;
    time: string;
  };
  unread_count: number;
  status: string;
  created_at: string;
  updated_at: string;
}

interface Message {
  id: number;
  sender: {
    id: number;
    name: string;
    photo?: string;
    is_me: boolean;
  };
  text: string;
  type: string;
  attachment_url?: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

// a helper to get user initials
const getInitials = (name: string) => {
  if (!name) return "";
  const allNames = name.trim().split(" ");
  const initials = allNames
    .slice(0, 2)
    .map((n: string) => n[0])
    .join("")
    .toUpperCase();
  return initials;
};

const MemberInboxPage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  // State management
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [messagesError, setMessagesError] = useState<string | null>(null);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [activeFilter, setActiveFilter] = useState<
    "all" | "unread" | "archived"
  >("all");
  const [newMessage, setNewMessage] = useState("");
  const [unreadCount, setUnreadCount] = useState(0);

  // Refs
  const messageInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load conversations on component mount
  useEffect(() => {
    loadConversations();
    loadUnreadCount();
  }, [activeFilter]);

  // Load messages when conversation is selected
  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id);
    }
  }, [selectedConversation]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/conversations",
        method: "GET",
        params: { filter: activeFilter },
      });

      console.log("Conversations API Response:", response);

      if (!response.error) {
        // Process and validate the data
        const conversationData = Array.isArray(response.data?.conversations)
          ? response.data.conversations.map((convo: any) => ({
              id: Number(convo.id || 0),
              listing: {
                id: Number(convo.listing?.id || 0),
                name: String(convo.listing?.name || ""),
                price: String(convo.listing?.price || "0"),
                image: convo.listing?.image || undefined,
              },
              other_user: {
                id: Number(convo.other_user?.id || 0),
                name: String(convo.other_user?.name || ""),
                photo: convo.other_user?.photo || undefined,
              },
              last_message: {
                text: String(convo.last_message?.text || ""),
                type: String(convo.last_message?.type || "text"),
                time: String(convo.last_message?.time || ""),
              },
              unread_count: Number(convo.unread_count || 0),
              status: String(convo.status || "active"),
              created_at: String(convo.created_at || ""),
              updated_at: String(convo.updated_at || ""),
            }))
          : [];

        setConversations(conversationData);

        // Select first conversation if none selected
        if (conversationData.length > 0 && !selectedConversation) {
          setSelectedConversation(conversationData[0]);
        }
      } else {
        setError(String(response.message || "Failed to load conversations"));
      }
    } catch (error: any) {
      console.error("Error loading conversations:", error);
      setError(String(error?.message || "An unexpected error occurred"));
    } finally {
      setLoading(false);
    }
  }, [sdk, activeFilter, selectedConversation]);

  const loadMessages = useCallback(
    async (conversationId: number) => {
      try {
        setMessagesLoading(true);
        setMessagesError(null);

        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/conversations/${conversationId}/messages`,
          method: "GET",
        });

        console.log("Messages API Response:", response);

        if (!response.error) {
          // Process and validate the messages data
          const messagesData = Array.isArray(response.data?.messages)
            ? response.data.messages.map((msg: any) => ({
                id: Number(msg.id || 0),
                sender: {
                  id: Number(msg.sender?.id || 0),
                  name: String(msg.sender?.name || ""),
                  photo: msg.sender?.photo || undefined,
                  is_me: Boolean(msg.sender?.is_me),
                },
                text: String(msg.text || ""),
                type: String(msg.type || "text"),
                attachment_url: msg.attachment_url || undefined,
                is_read: Boolean(msg.is_read),
                created_at: String(msg.created_at || ""),
                updated_at: String(msg.updated_at || ""),
              }))
            : [];

          setMessages(messagesData);
        } else {
          setMessagesError(
            String(response.message || "Failed to load messages")
          );
        }
      } catch (error: any) {
        console.error("Error loading messages:", error);
        setMessagesError(
          String(error?.message || "An unexpected error occurred")
        );
      } finally {
        setMessagesLoading(false);
      }
    },
    [sdk]
  );

  const loadUnreadCount = useCallback(async () => {
    try {
      // For now, just use mock data - no API calls
      // When you're ready to connect to real API, uncomment below:
      // const response = await sdk.request({
      //   endpoint: "/v2/api/ebadollar/custom/member/conversations/unread-count",
      //   method: "GET",
      // });
      // console.log("Unread Count API Response:", response);
      // if (!response.error) {
      //   setUnreadCount(Number(response.data?.unread_count || 0));
      // }

      // Mock data
      setUnreadCount(2);
    } catch (error: any) {
      console.error("Error loading unread count:", error);
    }
  }, []);

  const sendMessage = useCallback(async () => {
    if (!selectedConversation || !newMessage.trim() || sendingMessage) return;

    try {
      setSendingMessage(true);

      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/conversations/${selectedConversation.id}/messages`,
        method: "POST",
        body: { message_text: newMessage.trim() },
      });

      console.log("Send Message API Response:", response);

      if (!response.error) {
        // Add message to local state immediately for better UX
        const newMsg: Message = {
          id: Number(response.data?.message_id || 0),
          sender: {
            id: Number(response.data?.sender_id || 0),
            name: "You",
            is_me: true,
          },
          text: String(response.data?.message_text || newMessage.trim()),
          type: String(response.data?.message_type || "text"),
          is_read: false,
          created_at: String(
            response.data?.created_at || new Date().toISOString()
          ),
          updated_at: String(
            response.data?.created_at || new Date().toISOString()
          ),
        };

        setMessages((prev) => [...prev, newMsg]);
        setNewMessage("");

        // Refresh conversations to update last message
        loadConversations();
        success("Message sent successfully");
      } else {
        showError(String(response.message || "Failed to send message"));
      }
    } catch (error: any) {
      console.error("Error sending message:", error);
      showError(String(error?.message || "Failed to send message"));
    } finally {
      setSendingMessage(false);
    }
  }, [
    selectedConversation,
    newMessage,
    sendingMessage,
    sdk,
    loadConversations,
    success,
    showError,
  ]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (diffInHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const handleFileUpload = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file || !selectedConversation) return;

      // Validate file type
      if (!file.type.startsWith("image/")) {
        showError("Only image files are allowed");
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        showError("File size must be less than 5MB");
        return;
      }

      try {
        setSendingMessage(true);

        const formData = new FormData();
        formData.append("attachment", file);

        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/conversations/${selectedConversation.id}/attachments`,
          method: "POST",
          body: formData,
        });

        console.log("File Upload API Response:", response);

        if (!response.error) {
          // Add attachment message to local state
          const newMsg: Message = {
            id: Number(response.data?.message_id || 0),
            sender: {
              id: Number(response.data?.sender_id || 0),
              name: "You",
              is_me: true,
            },
            text: "",
            type: "image",
            attachment_url: String(response.data?.attachment_url || ""),
            is_read: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          setMessages((prev) => [...prev, newMsg]);

          // Refresh conversations to update last message
          loadConversations();

          success("Image sent successfully");
        } else {
          showError(String(response.message || "Failed to upload attachment"));
        }
      } catch (error: any) {
        console.error("Error uploading attachment:", error);
        showError(String(error?.message || "Failed to upload attachment"));
      } finally {
        setSendingMessage(false);
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    },
    [selectedConversation, sdk, loadConversations, success, showError]
  );

  return (
    <MemberWrapper>
      <div className="h-full" style={{ backgroundColor: "#0F2C59" }}>
        <div className="flex h-full gap-4 p-4">
          {/* Left Sidebar - Conversations List */}
          <div className="w-[350px] bg-white rounded-lg flex flex-col">
            <div className="p-4 pb-3">
              <h1 className="text-2xl font-bold text-black mb-4">Inbox</h1>
              <div className="flex">
                <button
                  className={`px-3 py-1 text-sm font-medium ${
                    activeFilter === "all"
                      ? "bg-[#0F2C59] text-white"
                      : "text-gray-600 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveFilter("all")}
                >
                  All
                </button>
                <button
                  className={`px-3 py-1 text-sm font-medium ${
                    activeFilter === "unread"
                      ? "bg-[#0F2C59] text-white"
                      : "text-gray-600 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveFilter("unread")}
                >
                  Unread {unreadCount > 0 && `(${unreadCount})`}
                </button>
                <button
                  className={`px-3 py-1 text-sm font-medium ${
                    activeFilter === "archived"
                      ? "bg-[#0F2C59] text-white"
                      : "text-gray-600 hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveFilter("archived")}
                >
                  Archived
                </button>
              </div>
            </div>
            <div className="flex-grow overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0F2C59]"></div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center p-8 text-red-500">
                  <div className="text-center">
                    <div className="text-4xl mb-2">⚠️</div>
                    <p className="text-sm mb-2">{error}</p>
                    <button
                      onClick={loadConversations}
                      className="px-4 py-2 bg-[#0F2C59] text-white rounded hover:bg-opacity-90 text-xs"
                    >
                      Retry
                    </button>
                  </div>
                </div>
              ) : conversations.length === 0 ? (
                <div className="flex items-center justify-center p-8 text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-2">💬</div>
                    <p className="text-sm">No conversations yet</p>
                    <p className="text-xs">
                      Start messaging about listings to see conversations here
                    </p>
                  </div>
                </div>
              ) : (
                conversations.map((convo) => (
                  <div
                    key={convo.id}
                    className={`flex items-start p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                      selectedConversation?.id === convo.id ? "bg-gray-50" : ""
                    }`}
                    onClick={() => setSelectedConversation(convo)}
                  >
                    <div className="w-10 h-10 rounded-full mr-3 bg-gray-300 flex items-center justify-center text-white font-bold text-sm">
                      {convo.other_user.photo ? (
                        <img
                          src={convo.other_user.photo}
                          alt={convo.other_user.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        getInitials(convo.other_user.name)
                      )}
                    </div>
                    <div className="flex-grow min-w-0">
                      <div className="flex justify-between items-start mb-1">
                        <h2 className="font-bold text-sm text-black truncate pr-2">
                          {convo.other_user.name}
                        </h2>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <span className="text-xs text-gray-500">
                            {formatTime(convo.last_message.time)}
                          </span>
                          {convo.unread_count > 0 && (
                            <span
                              className="text-xs text-white rounded-full h-4 w-4 flex items-center justify-center font-medium"
                              style={{ backgroundColor: "#E63946" }}
                            >
                              {convo.unread_count}
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-xs font-semibold text-gray-700 mb-1 truncate">
                        {convo.listing.name}
                      </p>
                      <p className="text-xs text-gray-600 truncate">
                        {convo.last_message.text}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Right Side - Conversation View */}
          <div className="flex-1 bg-white rounded-lg flex flex-col">
            {/* Header with Product Info */}
            {selectedConversation ? (
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center">
                  {selectedConversation.listing.image ? (
                    <img
                      src={selectedConversation.listing.image}
                      alt={selectedConversation.listing.name}
                      className="w-12 h-12 rounded-md mr-4 object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-md mr-4 bg-gray-300 flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l-1-1m5-5l-1-1"
                        ></path>
                      </svg>
                    </div>
                  )}
                  <div>
                    <h2 className="font-bold text-black">
                      {selectedConversation.listing.name}
                    </h2>
                    <p className="text-sm text-gray-600">
                      eBa$ {selectedConversation.listing.price}
                    </p>
                  </div>
                </div>
                <InteractiveButton className="px-4 !py-2 text-sm text-white rounded-lg bg-[#0F2C59] hover:bg-opacity-90">
                  View Listing
                </InteractiveButton>
              </div>
            ) : (
              <div className="p-4 border-b border-gray-200 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-4xl mb-2">💬</div>
                  <p className="text-sm">
                    Select a conversation to start messaging
                  </p>
                </div>
              </div>
            )}

            {/* Messages Area */}
            <div className="flex-grow p-4 overflow-y-auto">
              {selectedConversation ? (
                messagesLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0F2C59]"></div>
                  </div>
                ) : messagesError ? (
                  <div className="flex items-center justify-center h-full text-red-500">
                    <div className="text-center">
                      <div className="text-4xl mb-2">⚠️</div>
                      <p className="text-sm mb-2">{messagesError}</p>
                      <button
                        onClick={() =>
                          selectedConversation &&
                          loadMessages(selectedConversation.id)
                        }
                        className="px-4 py-2 bg-[#0F2C59] text-white rounded hover:bg-opacity-90 text-xs"
                      >
                        Retry
                      </button>
                    </div>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-2">💬</div>
                      <p className="text-sm">No messages yet</p>
                      <p className="text-xs">Start the conversation!</p>
                    </div>
                  </div>
                ) : (
                  <>
                    {messages.map((msg, index) => {
                      const showDateSeparator =
                        index === 0 ||
                        new Date(
                          messages[index - 1].created_at
                        ).toDateString() !==
                          new Date(msg.created_at).toDateString();

                      return (
                        <div key={msg.id}>
                          {showDateSeparator && (
                            <div className="text-center my-4">
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                {new Date(msg.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                          <div
                            className={`flex items-end gap-3 my-4 ${
                              msg.sender.is_me ? "justify-end" : "justify-start"
                            }`}
                          >
                            {!msg.sender.is_me && (
                              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-xs">
                                {msg.sender.photo ? (
                                  <img
                                    src={msg.sender.photo}
                                    alt={msg.sender.name}
                                    className="w-full h-full rounded-full object-cover"
                                  />
                                ) : (
                                  getInitials(msg.sender.name)
                                )}
                              </div>
                            )}
                            <div
                              className={`flex flex-col ${
                                msg.sender.is_me ? "items-end" : "items-start"
                              }`}
                            >
                              <div
                                className={`px-4 py-2 rounded-lg max-w-xs lg:max-w-md ${
                                  msg.sender.is_me
                                    ? "text-white bg-[#0F2C59]"
                                    : "bg-gray-200 text-black"
                                }`}
                              >
                                {msg.type === "image" && msg.attachment_url ? (
                                  <img
                                    src={msg.attachment_url}
                                    alt="Attachment"
                                    className="max-w-full h-auto rounded"
                                  />
                                ) : (
                                  <p className="text-sm">{msg.text}</p>
                                )}
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatTime(msg.created_at)}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    <div ref={messagesEndRef} />
                  </>
                )
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-2">💬</div>
                    <p className="text-sm">
                      Select a conversation to view messages
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Message Input Area */}
            {selectedConversation && (
              <div className="p-4 border-t border-gray-200 flex items-center gap-4">
                <div className="flex-grow flex items-center border border-gray-300 rounded-lg">
                  <button
                    className="p-2 text-gray-500 hover:text-gray-700"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={sendingMessage}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  <button className="p-2 text-gray-500 hover:text-gray-700">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-7.536 5.464a.5.5 0 01.708 0 2.5 2.5 0 003.536 0 .5.5 0 01.708-.708 3.5 3.5 0 01-4.95 0 .5.5 0 010-.708z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  <input
                    ref={messageInputRef}
                    type="text"
                    placeholder="Type a message..."
                    className="flex-grow p-2 text-sm bg-transparent focus:outline-none"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={sendingMessage}
                  />
                </div>
                <InteractiveButton
                  className="px-6 !py-2 text-sm text-white rounded-lg bg-[#E63946] hover:bg-opacity-90"
                  onClick={sendMessage}
                  disabled={sendingMessage || !newMessage.trim()}
                >
                  {sendingMessage ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Sending...
                    </div>
                  ) : (
                    "Send"
                  )}
                </InteractiveButton>

                {/* Hidden file input for attachments */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberInboxPage;
