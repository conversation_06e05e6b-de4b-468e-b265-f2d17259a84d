import React, { memo, useEffect } from "react";
import metadataJ<PERSON><PERSON> from "@/utils/metadata.json";
import { StringCaser } from "@/utils/utils";

interface MemberRouteProps {
  path: string;
  children: React.ReactNode;
}

const MemberRoute: React.FC<MemberRouteProps> = ({ path, children }) => {
  const stringCaser = new StringCaser();

  useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title
        ? stringCaser.Capitalize(metadata?.title, {
            separator: " ",
          })
        : "ebaDollar - Digital Marketplace";
    } else {
      document.title = "ebaDollar - Digital Marketplace";
    }
  }, [path, stringCaser]);

  return <>{children}</>;
};

export default memo(MemberRoute);
