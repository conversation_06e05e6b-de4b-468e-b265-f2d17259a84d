import React from "react";

interface DangerIconProps {
  className?: string;
}

export const CreateListingIcon = ({ className = "" }: DangerIconProps) => {
  return (
    <svg
      className={`${className}`}
      width="21"
      height="25"
      viewBox="0 0 21 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_1_135)">
        <path d="M21 24.9844H0V0.984375H21V24.9844Z" stroke="#E5E7EB" />
        <path
          d="M0 4.73459V11.7424C0 12.5393 0.314063 13.3033 0.876563 13.8658L9.12656 22.1158C10.2984 23.2877 12.1969 23.2877 13.3688 22.1158L19.6266 15.858C20.7984 14.6861 20.7984 12.7877 19.6266 11.6158L11.3766 3.36584C10.8141 2.80334 10.05 2.48927 9.25313 2.48927H2.25C1.00781 2.48459 0 3.4924 0 4.73459ZM5.25 6.23459C5.64783 6.23459 6.02936 6.39262 6.31066 6.67393C6.59197 6.95523 6.75 7.33676 6.75 7.73459C6.75 8.13241 6.59197 8.51394 6.31066 8.79525C6.02936 9.07655 5.64783 9.23459 5.25 9.23459C4.85218 9.23459 4.47064 9.07655 4.18934 8.79525C3.90804 8.51394 3.75 8.13241 3.75 7.73459C3.75 7.33676 3.90804 6.95523 4.18934 6.67393C4.47064 6.39262 4.85218 6.23459 5.25 6.23459Z"
          fill="#F52D2A"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_135">
          <rect
            width="21"
            height="24"
            fill="white"
            transform="translate(0 0.984375)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
