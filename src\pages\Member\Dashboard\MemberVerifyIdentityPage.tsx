import { useState, useRef, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useToast } from "@/hooks/useToast";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { CameraToUpload } from "@/components/CameraToUpload";
// @ts-ignore
import { ReactMic } from "react-mic";

const MemberVerifyIdentityPage = () => {
  const { success, error: showError } = useToast();
  const navigate = useNavigate();
  const { sdk } = useSDK();

  const [frontIdFile, setFrontIdFile] = useState<File | null>(null);
  const [backIdFile, setBackIdFile] = useState<File | null>(null);
  const [selfieFile, setSelfieFile] = useState<File | null>(null);
  const [voiceFile, setVoiceFile] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<{
    [key: string]: boolean;
  }>({});
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [requirements, setRequirements] = useState<any>(null);

  // Add state for react-mic
  const [recordedVoice, setRecordedVoice] = useState<Blob | null>(null);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const frontIdInputRef = useRef<HTMLInputElement>(null);
  const backIdInputRef = useRef<HTMLInputElement>(null);
  const selfieInputRef = useRef<HTMLInputElement>(null);
  const voiceInputRef = useRef<HTMLInputElement>(null);

  const loadVerificationData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load verification status and requirements in parallel
      const [statusResponse, requirementsResponse] = await Promise.all([
        sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/verification/status",
          method: "GET",
        }),
        sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/verification/requirements",
          method: "GET",
        }),
      ]);

      console.log("Verification Status API Response:", statusResponse);
      console.log(
        "Verification Requirements API Response:",
        requirementsResponse
      );

      if (!statusResponse.error) {
        setVerificationStatus(statusResponse.data);

        // Set existing files from status
        const documents = statusResponse.data?.documents || {};
        if (documents.government_id_front) {
          // Create a mock file object for display purposes
          setFrontIdFile(new File([], "government_id_front.jpg"));
        }
        if (documents.government_id_back) {
          setBackIdFile(new File([], "government_id_back.jpg"));
        }
        if (documents.selfie_verification) {
          setSelfieFile(new File([], "selfie_verification.jpg"));
        }
        if (documents.voice_verification) {
          setVoiceFile(new File([], "voice_verification.mp3"));
        }
      } else {
        setError(
          String(statusResponse.message || "Failed to load verification status")
        );
      }

      if (!requirementsResponse.error) {
        setRequirements(requirementsResponse.data);
      } else if (!statusResponse.error) {
        // Only set error if status also failed
        setError(
          String(
            requirementsResponse.message ||
              "Failed to load verification requirements"
          )
        );
      }
    } catch (error: any) {
      console.error("Error loading verification data:", error);
      setError(String(error?.message || "Failed to load verification data"));
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  const handleFileUpload = useCallback(
    async (
      event: React.ChangeEvent<HTMLInputElement>,
      type: "front" | "back" | "selfie"
    ) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      const validImageTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validImageTypes.includes(file.type)) {
        showError("Please upload a JPG or PNG image");
        return;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        showError("File size must be less than 10MB");
        return;
      }

      const documentTypeMap = {
        front: "government_id_front",
        back: "government_id_back",
        selfie: "selfie_verification",
      };

      const documentType = documentTypeMap[type];

      try {
        setUploadingFiles((prev) => ({ ...prev, [type]: true }));

        const formData = new FormData();
        formData.append("file", file);
        formData.append("document_type", documentType);

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Document API Response:", response);

        if (!response.error) {
          // Update local state
          switch (type) {
            case "front":
              setFrontIdFile(file);
              break;
            case "back":
              setBackIdFile(file);
              break;
            case "selfie":
              setSelfieFile(file);
              break;
          }

          success("Document uploaded successfully");

          // Refresh verification status
          await loadVerificationData();
        } else {
          showError(String(response.message || "Failed to upload document"));
        }
      } catch (error: any) {
        console.error("Error uploading document:", error);
        showError(String(error?.message || "Failed to upload document"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, [type]: false }));
      }
    },
    [success, showError, loadVerificationData]
  );

  const handleStartCamera = useCallback(() => {
    // In a real implementation, this would open camera
    showError("Camera functionality would be implemented here");
  }, [showError]);

  const handleStartRecording = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // In a real implementation, this would stop recording and save the file
      success("Recording stopped");
    } else {
      // Start recording
      setIsRecording(true);
      success("Recording started");
    }
  }, [isRecording, success]);

  const handleVoiceFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      const validAudioTypes = [
        "audio/mpeg",
        "audio/wav",
        "audio/mp3",
        "audio/ogg",
      ];
      if (!validAudioTypes.includes(file.type)) {
        showError("Please upload an audio file (MP3, WAV, OGG)");
        return;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        showError("File size must be less than 10MB");
        return;
      }

      try {
        setUploadingFiles((prev) => ({ ...prev, voice: true }));

        const formData = new FormData();
        formData.append("file", file);
        formData.append("document_type", "voice_verification");

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Voice API Response:", response);

        if (!response.error) {
          setVoiceFile(file);
          success("Voice recording uploaded successfully");

          // Refresh verification status
          await loadVerificationData();
        } else {
          showError(
            String(response.message || "Failed to upload voice recording")
          );
        }
      } catch (error: any) {
        console.error("Error uploading voice recording:", error);
        showError(String(error?.message || "Failed to upload voice recording"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, voice: false }));
      }
    },
    [success, showError, loadVerificationData]
  );

  // Add handler for react-mic stop
  const onVoiceStop = useCallback(
    async (recordedData: any) => {
      if (!recordedData.blob) {
        showError("No voice recording data received");
        return;
      }

      setRecordedVoice(recordedData.blob);

      // Create audio URL for playback
      const url = URL.createObjectURL(recordedData.blob);
      setAudioUrl(url);

      setUploadingFiles((prev) => ({ ...prev, voice: true }));

      try {
        const formData = new FormData();
        formData.append("file", recordedData.blob, "voice_verification.webm");
        formData.append("document_type", "voice_verification");

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Recorded Voice API Response:", response);

        if (!response.error) {
          setVoiceFile(
            new File([recordedData.blob], "voice_verification.webm")
          );
          success("Voice recording uploaded successfully");
          await loadVerificationData();
        } else {
          showError(
            String(response.message || "Failed to upload voice recording")
          );
        }
      } catch (error: any) {
        console.error("Error uploading recorded voice:", error);
        showError(String(error?.message || "Failed to upload voice recording"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, voice: false }));
      }
    },
    [success, showError, loadVerificationData, sdk]
  );

  const handleCompleteVerification = useCallback(async () => {
    try {
      setSubmitLoading(true);

      // Check if required documents are uploaded
      if (!frontIdFile || !backIdFile || !selfieFile) {
        showError(
          "Please upload all required documents (Front ID, Back ID, and Selfie)"
        );
        return;
      }

      // Submit verification for review
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/verification/submit",
        method: "POST",
      });

      console.log("Submit Verification API Response:", response);

      if (!response.error) {
        success(
          "Verification submitted successfully! We'll review your documents within 24 hours."
        );
        navigate("/member/dashboard");
      } else {
        showError(String(response.message || "Failed to submit verification"));
      }
    } catch (error: any) {
      console.error("Error submitting verification:", error);
      showError(String(error?.message || "Verification submission failed"));
    } finally {
      setSubmitLoading(false);
    }
  }, [frontIdFile, backIdFile, selfieFile, success]);

  const handleSkipForNow = () => {
    navigate("/member/dashboard");
  };

  const handleBack = useCallback(() => {
    navigate("/member/dashboard");
  }, [navigate]);

  // Handle audio playback
  const handlePlayAudio = useCallback(() => {
    if (audioUrl) {
      const audio = new Audio(audioUrl);
      setIsPlaying(true);

      audio.onended = () => {
        setIsPlaying(false);
      };

      audio.onerror = () => {
        setIsPlaying(false);
        showError("Error playing audio");
      };

      audio.play().catch((error) => {
        console.error("Error playing audio:", error);
        setIsPlaying(false);
        showError("Error playing audio");
      });
    }
  }, [audioUrl, showError]);

  // Cleanup audio URL on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Load verification status and requirements on mount
  useEffect(() => {
    loadVerificationData();
  }, []);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E63946] mx-auto mb-4"></div>
          <p className="text-white">Loading verification data...</p>
        </div>
      </div>
    );
  }

  if (error && !verificationStatus) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadVerificationData}
            className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="flex h-screen">
      {/* Left Sidebar */}
      <div className="w-[250px] bg-[#0F2C59] flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-600">
          <h1 className="text-2xl font-bold space-x-1">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>

        {/* Progress Steps */}
        <div className="p-6 flex-1">
          <div className="space-y-6">
            {/* Basic Information Step - Completed */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
              <div>
                <div className="text-[#22C55E] font-medium">
                  Basic Information
                </div>
              </div>
            </div>

            {/* Verify Identity Step - Current */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#E63946] flex items-center justify-center">
                {/* <span className="text-white text-xs font-bold">2</span> */}
              </div>
              <div>
                <div className="text-white font-medium">Verify Identity</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white p-8 h-screen overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h2 className="text-4xl font-bold text-[#1A202C] mb-2">
              Verify your identity
            </h2>
            <p className="text-[#6B7280]">
              Complete the verification process to secure your account and
              unlock all features.
            </p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-[#1A202C]">
                Identity Verification
              </span>
              <span className="text-sm font-medium text-[#E63946]">
                50% Complete
              </span>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-xs text-gray-600">Step 2 of 2</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-[#E63946] h-2 rounded-full"
                style={{ width: "50%" }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Basic Info</span>
              <span>Verify Identity</span>
            </div>
          </div>

          <div className="space-y-8">
            {/* ID Document Verification */}
            <div className="bg-[#EFF6FF] rounded-lg p-6 border border-[#BFDBFE]">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
                      fill="white"
                    />
                    <path
                      d="M14 2V8H20"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    ID Document Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Upload clear photos of your government-issued ID
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Front of ID */}
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-white flex flex-col items-center">
                    <div className="text-4xl text-gray-400 mb-2">
                      <svg
                        width="48"
                        height="48"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="16"
                          rx="2"
                          fill="#D1D5DB"
                        />
                        <rect
                          x="7"
                          y="8"
                          width="10"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <rect
                          x="7"
                          y="11"
                          width="6"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <rect
                          x="7"
                          y="14"
                          width="8"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <circle cx="16" cy="10" r="2" fill="#9CA3AF" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Front of ID
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Upload the front side of your ID document
                    </p>

                    <p className="text-sm text-green-600 mb-2">
                      {frontIdFile ? (
                        <span>✓ {frontIdFile.name}</span>
                      ) : (
                        <span className=" opacity-0">✓ </span>
                      )}
                    </p>

                    {audioUrl && (
                      <p className="text-sm text-green-600 mb-2">
                        <span>✓ {audioUrl}</span>
                      </p>
                    )}

                    <InteractiveButton
                      className="!bg-[#2563EB] !h-[48px] hover:!bg-blue-600 !text-white !px-6 !py-3 !rounded-md !font-medium"
                      onClick={() => frontIdInputRef.current?.click()}
                      disabled={uploadingFiles.front}
                    >
                      {uploadingFiles.front ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Take Photo / Upload
                        </>
                      )}
                    </InteractiveButton>
                    <input
                      ref={frontIdInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleFileUpload(e, "front")}
                      className="hidden"
                    />
                    <p className="text-xs text-gray-500 mt-2">
                      JPG, PNG up to 10MB
                    </p>
                  </div>
                </div>

                {/* Back of ID */}
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-white flex flex-col items-center">
                    <div className="text-4xl text-gray-400 mb-2">
                      <svg
                        width="48"
                        height="48"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="16"
                          rx="2"
                          fill="#D1D5DB"
                        />
                        <rect
                          x="7"
                          y="8"
                          width="10"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <rect
                          x="7"
                          y="11"
                          width="6"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <rect
                          x="7"
                          y="14"
                          width="8"
                          height="1"
                          fill="#9CA3AF"
                        />
                        <circle cx="16" cy="10" r="2" fill="#9CA3AF" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Back of ID
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Upload the back side of your ID document
                    </p>

                    <p className="text-sm text-green-600 mb-2">
                      {backIdFile ? (
                        <span>✓ {backIdFile.name}</span>
                      ) : (
                        <span className=" opacity-0">✓ </span>
                      )}
                    </p>

                    <InteractiveButton
                      className="!bg-[#2563EB] !h-[48px] hover:!bg-blue-600 !text-white !px-6 !py-3 !rounded-md !font-medium"
                      onClick={() => backIdInputRef.current?.click()}
                      disabled={uploadingFiles.back}
                    >
                      {uploadingFiles.back ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Take Photo / Upload
                        </>
                      )}
                    </InteractiveButton>
                    <input
                      ref={backIdInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleFileUpload(e, "back")}
                      className="hidden"
                    />
                    <p className="text-xs text-gray-500 mt-2">
                      JPG, PNG up to 10MB
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 rounded-md p-4 mt-6">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <span className="text-white text-xs font-bold">i</span>
                  </div>
                  <p className="text-sm text-blue-800">
                    <strong>Accepted documents:</strong> Driver's license,
                    passport, national ID card, or state-issued ID
                  </p>
                </div>
              </div>
            </div>

            {/* Selfie Verification */}
            <div className="bg-[#F0FDF4] rounded-lg p-6 border border-[#BBF7D0]">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Selfie Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Take a selfie to verify your identity matches your ID
                  </p>
                </div>
              </div>
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-200 bg-white rounded-lg p-8 flex flex-col items-center">
                  <div className="text-4xl text-gray-400 mb-3">
                    <svg
                      width="96"
                      height="96"
                      viewBox="0 0 96 96"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z"
                        fill="#F3F4F6"
                      />
                      <path
                        d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z"
                        stroke="#E5E7EB"
                      />
                      <path
                        d="M61.125 66H34.875V30H61.125V66Z"
                        stroke="#E5E7EB"
                      />
                      <g clip-path="url(#clip0_319_1957)">
                        <path
                          d="M48 47.75C49.9891 47.75 51.8968 46.9598 53.3033 45.5533C54.7098 44.1468 55.5 42.2391 55.5 40.25C55.5 38.2609 54.7098 36.3532 53.3033 34.9467C51.8968 33.5402 49.9891 32.75 48 32.75C46.0109 32.75 44.1032 33.5402 42.6967 34.9467C41.2902 36.3532 40.5 38.2609 40.5 40.25C40.5 42.2391 41.2902 44.1468 42.6967 45.5533C44.1032 46.9598 46.0109 47.75 48 47.75ZM45.3223 50.5625C39.5508 50.5625 34.875 55.2383 34.875 61.0098C34.875 61.9707 35.6543 62.75 36.6152 62.75H59.3848C60.3457 62.75 61.125 61.9707 61.125 61.0098C61.125 55.2383 56.4492 50.5625 50.6777 50.5625H45.3223Z"
                          fill="#9CA3AF"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_319_1957">
                          <path
                            d="M34.875 32.75H61.125V62.75H34.875V32.75Z"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Take a Selfie
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Look directly at the camera and ensure your face is clearly
                    visible
                  </p>
                  <div className=" w-full ml-28   flex flex-col items-center ">
                    <CameraToUpload
                      textAreaColor="bg-[#16A34A]"
                      borderColor="border-[#16A34A]"
                      uploadSuccess={async (results: string[]) => {
                        if (results && results.length > 0) {
                          // Upload to the correct API endpoint
                          setUploadingFiles((prev) => ({
                            ...prev,
                            selfie: true,
                          }));
                          try {
                            const formData = new FormData();
                            const blob = await fetch(results[0]).then((r) =>
                              r.blob()
                            );
                            formData.append(
                              "file",
                              blob,
                              "selfie_verification.jpg"
                            );
                            formData.append(
                              "document_type",
                              "selfie_verification"
                            );

                            const response = await sdk.request({
                              endpoint:
                                "/v2/api/ebadollar/custom/member/verification/upload-document",
                              method: "POST",
                              body: formData,
                            });

                            console.log(
                              "Upload Selfie Camera API Response:",
                              response
                            );

                            if (!response.error) {
                              setSelfieFile(
                                new File([blob], "selfie_verification.jpg")
                              );
                              success("Selfie uploaded successfully");
                              await loadVerificationData();
                            } else {
                              showError(
                                String(
                                  response.message || "Failed to upload selfie"
                                )
                              );
                            }
                          } catch (error: any) {
                            console.error("Error uploading selfie:", error);
                            showError(
                              String(
                                error?.message || "Failed to upload selfie"
                              )
                            );
                          } finally {
                            setUploadingFiles((prev) => ({
                              ...prev,
                              selfie: false,
                            }));
                          }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="bg-green-50 rounded-md p-4 mt-6">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9.663 17h4.673L12 3l-2.337 14zM10 3l2.337 14h-4.674L10 3z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-green-800">
                    <strong>Tips:</strong> Ensure good lighting, remove
                    glasses/hat, and look directly at the camera
                  </p>
                </div>
              </div>
            </div>

            {/* Voice Verification */}
            <div className="bg-[#FAF5FF] rounded-lg p-6 border border-[#E9D5FF] ">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-6 h-6 bg-purple-500 rounded flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Voice Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Record a short voice message to prove you're a real person
                  </p>
                </div>
              </div>

              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-white flex flex-col items-center">
                  <div className="text-4xl text-gray-400 mb-3">
                    <svg
                      width="96"
                      height="96"
                      viewBox="0 0 96 96"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z"
                        fill="#F3F4F6"
                      />
                      <path
                        d="M48 0C74.5097 0 96 21.4903 96 48C96 74.5097 74.5097 96 48 96C21.4903 96 0 74.5097 0 48C0 21.4903 21.4903 0 48 0Z"
                        stroke="#E5E7EB"
                      />
                      <path d="M59.25 66H36.75V30H59.25V66Z" stroke="#E5E7EB" />
                      <g clip-path="url(#clip0_319_1976)">
                        <path
                          d="M48 32.75C44.8945 32.75 42.375 35.2695 42.375 38.375V47.75C42.375 50.8555 44.8945 53.375 48 53.375C51.1055 53.375 53.625 50.8555 53.625 47.75V38.375C53.625 35.2695 51.1055 32.75 48 32.75ZM40.5 45.4062C40.5 44.627 39.873 44 39.0938 44C38.3145 44 37.6875 44.627 37.6875 45.4062V47.75C37.6875 52.9707 41.5664 57.2832 46.5938 57.9688V59.9375H43.7812C43.002 59.9375 42.375 60.5645 42.375 61.3438C42.375 62.123 43.002 62.75 43.7812 62.75H48H52.2188C52.998 62.75 53.625 62.123 53.625 61.3438C53.625 60.5645 52.998 59.9375 52.2188 59.9375H49.4062V57.9688C54.4336 57.2832 58.3125 52.9707 58.3125 47.75V45.4062C58.3125 44.627 57.6855 44 56.9062 44C56.127 44 55.5 44.627 55.5 45.4062V47.75C55.5 51.8926 52.1426 55.25 48 55.25C43.8574 55.25 40.5 51.8926 40.5 47.75V45.4062Z"
                          fill="#9CA3AF"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_319_1976">
                          <path
                            d="M36.75 32.75H59.25V62.75H36.75V32.75Z"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Record Voice Message
                  </h4>
                  <div className="bg-gray-100 rounded-md p-3 mb-3">
                    <p className="text-sm text-gray-700">
                      "
                      {requirements?.verification_phrase ||
                        "I am [Your Full Name] and I am creating an account with eBaDollar on [Today's Date]"}
                      "
                    </p>
                  </div>
                  <p className="text-xs text-gray-500 mb-4">
                    Please read the phrase above clearly
                  </p>
                  {voiceFile && (
                    <div className="mb-4">
                      <p className="text-sm text-green-600 mb-2">
                        ✓ Voice message recorded
                      </p>
                      {audioUrl && (
                        <InteractiveButton
                          className="!px-4 !py-2 !text-purple-600 !bg-white !border !border-purple-600 hover:!bg-purple-50 !rounded-md !text-sm !font-medium"
                          onClick={handlePlayAudio}
                          disabled={isPlaying}
                        >
                          {isPlaying ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600 mr-2 inline-block"></div>
                              Playing...
                            </>
                          ) : (
                            <>
                              <svg
                                className="w-3 h-3 mr-2"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9l9-4.5L7 0v9z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              Play Recording
                            </>
                          )}
                        </InteractiveButton>
                      )}
                    </div>
                  )}

                  <InteractiveButton
                    className={`!px-6 !py-3 !h-[60px] !w-[220px] !text-white !rounded-md !font-medium ${isVoiceRecording ? "!bg-red-500 hover:!bg-red-600" : "!bg-[#9333EA] hover:!bg-purple-600"}`}
                    onClick={() => setIsVoiceRecording((prev) => !prev)}
                    disabled={uploadingFiles.voice}
                  >
                    {uploadingFiles.voice ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Uploading...
                      </>
                    ) : isVoiceRecording ? (
                      <>
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Stop Recording
                      </>
                    ) : (
                      <>
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                          />
                        </svg>
                        Start Recording
                      </>
                    )}
                  </InteractiveButton>

                  {/* Hidden ReactMic component for actual recording */}
                  <div className="hidden">
                    <ReactMic
                      record={isVoiceRecording}
                      className="w-full"
                      onStop={onVoiceStop}
                      mimeType="audio/webm"
                      strokeColor="#6D28D9"
                      backgroundColor="#F3E8FF"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-md p-4 mt-6">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-purple-800">
                    <strong>Security:</strong> Voice verification helps prevent
                    automated accounts and ensures real human interaction
                  </p>
                </div>
              </div>
            </div>

            {/* Verification Processing Time */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6  mt-0.5">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_319_1912)">
                      <g clip-path="url(#clip1_319_1912)">
                        <path
                          d="M8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0ZM7.25 3.75V8C7.25 8.25 7.375 8.48438 7.58437 8.625L10.5844 10.625C10.9281 10.8562 11.3938 10.7625 11.625 10.4156C11.8562 10.0687 11.7625 9.60625 11.4156 9.375L8.75 7.6V3.75C8.75 3.33437 8.41562 3 8 3C7.58437 3 7.25 3.33437 7.25 3.75Z"
                          fill="#3B82F6"
                        />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_319_1912">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                      <clipPath id="clip1_319_1912">
                        <path d="M0 0H16V16H0V0Z" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                  {/* <span className="text-white text-xs">ℹ</span> */}
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">
                    Verification Processing Time
                  </h4>
                  <p className="text-sm text-blue-700">
                    Your verification will be processed within 24 hours. You'll
                    receive an email notification once approved.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-8 mt-8 border-t border-gray-200">
            <button
              type="button"
              className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
              onClick={handleBack}
            >
              Back
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                className="px-6 py-3 text-sm font-medium text-gray-700 bg-[#E5E7EB] border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                onClick={handleSkipForNow}
              >
                Skip for Now
              </button>

              <InteractiveButton
                className="!bg-[#E63946] hover:!bg-[#d63384] focus:!ring-[#E63946] !px-6 h-[46px] !font-medium"
                loading={submitLoading}
                disabled={submitLoading}
                onClick={handleCompleteVerification}
              >
                Complete Verification
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default MemberVerifyIdentityPage;
