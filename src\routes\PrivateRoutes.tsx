import React, { memo } from "react";
import { NotFound } from "./Routes";
import PublicRoute from "./PublicRoutes";
import AdminRoute from "./AdminRoutes";
import MemberRoute from "./MemberRoutes";
import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";

interface PrivateRouteProps {
  path: string;
  element: JSX.Element;
  access: string[] | string;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({
  path,
  element,
  access,
}: PrivateRouteProps) => {
  const { authState } = useContexts();

  if (authState?.isAuthenticated) {
    const accessArray = typeof access === "string" ? [access] : access;
    const userRole = authState?.role as RoleEnum;

    // Check if user has access to this route
    if (accessArray.includes(userRole)) {
      // Route to appropriate wrapper based on role
      if ([RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(userRole)) {
        return <AdminRoute path={path}>{element}</AdminRoute>;
      } else if (userRole === RoleEnum.MEMBER) {
        // For member routes, use MemberRoute wrapper for metadata management
        return <MemberRoute path={path}>{element}</MemberRoute>;
      }
    }

    // If user doesn't have access, show not found
    return <PublicRoute path={"*"} element={<NotFound />} />;
  }

  // If not authenticated, show not found
  return <PublicRoute path={"*"} element={<NotFound />} />;
};

export default memo(PrivateRoute);
