import React from "react";
import {
  BalanceImg,
  CreditLineImg,
  CreditLineBalanceImg,
  CommissionsImg,
  EbaRewardsImg,
} from "@/assets/images";

export interface DashboardStatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  type:
    | "balance"
    | "credit"
    | "credit-balance"
    | "rating"
    | "commissions"
    | "rewards";
  monthlyChange?: number;
  utilizationPercentage?: number;
  dueDate?: string | null;
  balanceDue?: number;
  averageRating?: number;
  totalRatings?: number;
  activeReferrals?: number;
  monthlyAmount?: string;
  onAction?: () => void;
  actionText?: string;
  actionDisabled?: boolean;
  formatCurrency?: (amount: number) => string;
  formatTransactionAmount?: (amount: number) => string;
  renderStars?: (rating: number) => React.ReactNode;
  icon?: React.ReactNode;
}

const DashboardStatCard: React.FC<DashboardStatCardProps> = ({
  title,
  value,
  subtitle,
  type,
  monthlyChange,
  utilizationPercentage,
  dueDate,
  balanceDue = 0,
  averageRating,
  totalRatings,
  activeReferrals,
  monthlyAmount,
  onAction,
  actionText,
  actionDisabled = false,
  formatCurrency = (amount: number) => `eBa$${amount.toFixed(2)}`,
  formatTransactionAmount = (amount: number) =>
    `${amount >= 0 ? "+" : ""}eBa$${amount.toFixed(2)}`,
  renderStars = () => null,
  icon,
}) => {
  const renderBalanceCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <div className="w-8 h-8 flex items-center justify-center">
          {icon || (
            <img
              src={BalanceImg}
              alt="Balance"
              className="w-6 h-6 object-cover"
            />
          )}
        </div>
      </div>
      <p className="text-3xl font-bold">{value}</p>
      {monthlyChange !== undefined && (
        <p
          className={`text-sm mb-2 ${
            monthlyChange >= 0 ? "text-green-500" : "text-red-500"
          }`}
        >
          {formatTransactionAmount(monthlyChange)} this month
        </p>
      )}
      <div className="w-full bg-gray-200 rounded-full h-1">
        <div
          className={`${+value >= 0 ? "bg-green-500" : ""}  h-1 rounded-full`}
          style={{
            width: `${Math.min(100, (Number(value) / 5000) * 100)}%`,
          }}
        ></div>
      </div>
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>eBa$ 0</span>
        <span>{value}</span>
      </div>
      <p className="text-xs text-gray-400">Current account balance</p>
    </>
  );

  const renderCreditCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <div className="w-8 h-8 flex items-center justify-center">
          <img
            src={CreditLineImg}
            alt="Credit Line"
            className="w-6 h-6 object-cover"
          />
        </div>
      </div>
      <p className="text-3xl font-bold">{value}</p>
      {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
      {utilizationPercentage !== undefined && (
        <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
          <div
            className="bg-blue-500 h-1 rounded-full"
            style={{
              width: `${utilizationPercentage}%`,
            }}
          ></div>
        </div>
      )}
      <div className="text-xs text-gray-500 mt-1">
        Used:{" "}
        {formatCurrency(
          Number(value) -
            (utilizationPercentage
              ? (Number(value) * (100 - utilizationPercentage)) / 100
              : 0)
        )}{" "}
        Available:{" "}
        {formatCurrency(
          utilizationPercentage
            ? (Number(value) * (100 - utilizationPercentage)) / 100
            : Number(value)
        )}
      </div>
      <p className="text-xs text-gray-400">Credit line utilization</p>
    </>
  );

  const renderCreditBalanceCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <div className="w-8 h-8 flex items-center justify-center">
          <img
            src={CreditLineBalanceImg}
            alt="Credit Line Balance"
            className="w-6 h-6 object-cover"
          />
        </div>
      </div>
      <p className="text-3xl font-bold">{value}</p>
      <p className="text-sm text-gray-500 mb-4">
        {dueDate
          ? `Due ${new Date(dueDate).toLocaleDateString()}`
          : "No due date"}
      </p>
      {onAction && (
        <button
          onClick={onAction}
          className="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 w-full disabled:bg-gray-400 disabled:cursor-not-allowed"
          disabled={actionDisabled || balanceDue === 0}
        >
          {balanceDue === 0 ? "No Balance Due" : actionText || "Pay Balance"}
        </button>
      )}
    </>
  );

  const renderRatingCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <span className="text-yellow-400 text-xl">★</span>
      </div>
      <div className="flex items-center">
        <p className="text-3xl font-bold mr-2">{averageRating?.toFixed(1)}</p>
        <div className="flex">{renderStars(averageRating || 0)}</div>
      </div>
      <p className="text-sm text-gray-500">
        Based on {totalRatings} transactions
      </p>
    </>
  );

  const renderCommissionsCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <div className="w-8 h-8 flex items-center justify-center">
          <img
            src={CommissionsImg}
            alt="Commissions"
            className="w-6 h-6 object-cover"
          />
        </div>
      </div>
      <p className="text-3xl font-bold">{value}</p>
      <p className="text-sm text-gray-500">
        {activeReferrals} active referrals
      </p>
    </>
  );

  const renderRewardsCard = () => (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">{title}</h3>
        <div className="w-8 h-8 flex items-center justify-center">
          <img
            src={EbaRewardsImg}
            alt="Rewards"
            className="w-6 h-6 object-cover"
          />
        </div>
      </div>
      <p className="text-3xl font-bold">{value}</p>
      <p className="text-sm text-gray-500">{monthlyAmount}</p>
    </>
  );

  const renderCardContent = () => {
    switch (type) {
      case "balance":
        return renderBalanceCard();
      case "credit":
        return renderCreditCard();
      case "credit-balance":
        return renderCreditBalanceCard();
      case "rating":
        return renderRatingCard();
      case "commissions":
        return renderCommissionsCard();
      case "rewards":
        return renderRewardsCard();
      default:
        return null;
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow text-gray-800 flex flex-col justify-between">
      {renderCardContent()}
    </div>
  );
};

export default DashboardStatCard;
